{"version": 3, "file": "ProgressDifferentialDownloadCallbackTransform.js", "sourceRoot": "", "sources": ["../../src/differentialDownloader/ProgressDifferentialDownloadCallbackTransform.ts"], "names": [], "mappings": ";;;AAAA,mCAAkC;AAGlC,IAAK,aAGJ;AAHD,WAAK,aAAa;IAChB,iDAAI,CAAA;IACJ,yDAAQ,CAAA;AACV,CAAC,EAHI,aAAa,KAAb,aAAa,QAGjB;AAeD,MAAa,6CAA8C,SAAQ,kBAAS;IAU1E,YACmB,gCAAkE,EAClE,iBAAoC,EACpC,UAAuC;QAExD,KAAK,EAAE,CAAA;QAJU,qCAAgC,GAAhC,gCAAgC,CAAkC;QAClE,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,eAAU,GAAV,UAAU,CAA6B;QAZlD,UAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAClB,gBAAW,GAAG,CAAC,CAAA;QACf,UAAK,GAAG,CAAC,CAAA;QACT,kBAAa,GAAG,CAAC,CAAA;QACjB,UAAK,GAAG,CAAC,CAAA;QACT,kBAAa,GAAG,aAAa,CAAC,IAAI,CAAA;QAElC,eAAU,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;IAQtC,CAAC;IAED,UAAU,CAAC,KAAU,EAAE,QAAgB,EAAE,QAAa;QACpD,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YACrC,QAAQ,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,CAAA;YACtC,OAAM;QACR,CAAC;QAED,oDAAoD;QACpD,IAAI,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,IAAI,EAAE,CAAC;YAC7C,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;YACrB,OAAM;QACR,CAAC;QAED,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,MAAM,CAAA;QAChC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAA;QAE1B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,IACE,GAAG,IAAI,IAAI,CAAC,UAAU;YACtB,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,aAAa,CAAC,2CAA2C;YACnF,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,gCAAgC,CAAC,UAAU,CAAC,+BAA+B,EACrG,CAAC;YACD,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,IAAI,CAAA;YAE5B,IAAI,CAAC,UAAU,CAAC;gBACd,KAAK,EAAE,IAAI,CAAC,gCAAgC,CAAC,UAAU;gBACvD,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,OAAO,EAAE,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gCAAgC,CAAC,UAAU,CAAC,GAAG,GAAG;gBACpF,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;aAC3E,CAAC,CAAA;YACF,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;QAChB,CAAC;QAED,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IACvB,CAAC;IAED,aAAa;QACX,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,IAAI,CAAA;IACzC,CAAC;IAED,kBAAkB;QAChB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,QAAQ,CAAA;QAE3C,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,gCAAgC,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;IAC9F,CAAC;IAED,gBAAgB;QACd,iCAAiC;QACjC,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,gCAAgC,CAAC,UAAU,EAAE,CAAC;YAC1E,IAAI,CAAC,UAAU,CAAC;gBACd,KAAK,EAAE,IAAI,CAAC,gCAAgC,CAAC,UAAU;gBACvD,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,OAAO,EAAE,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gCAAgC,CAAC,UAAU,CAAC,GAAG,GAAG;gBACpF,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;aAClF,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,4DAA4D;IAC5D,MAAM,CAAC,QAAa;QAClB,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YACrC,QAAQ,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC,CAAA;YAChC,OAAM;QACR,CAAC;QAED,IAAI,CAAC,UAAU,CAAC;YACd,KAAK,EAAE,IAAI,CAAC,gCAAgC,CAAC,UAAU;YACvD,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,OAAO,EAAE,GAAG;YACZ,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;SAClF,CAAC,CAAA;QACF,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;QACd,IAAI,CAAC,WAAW,GAAG,CAAC,CAAA;QAEpB,QAAQ,CAAC,IAAI,CAAC,CAAA;IAChB,CAAC;CACF;AAhGD,sGAgGC", "sourcesContent": ["import { Transform } from \"stream\"\nimport { CancellationToken } from \"builder-util-runtime\"\n\nenum OperationKind {\n  COPY,\n  DOWNLOAD,\n}\n\nexport interface ProgressInfo {\n  total: number\n  delta: number\n  transferred: number\n  percent: number\n  bytesPerSecond: number\n}\n\nexport interface ProgressDifferentialDownloadInfo {\n  expectedByteCounts: Array<number>\n  grandTotal: number\n}\n\nexport class ProgressDifferentialDownloadCallbackTransform extends Transform {\n  private start = Date.now()\n  private transferred = 0\n  private delta = 0\n  private expectedBytes = 0\n  private index = 0\n  private operationType = OperationKind.COPY\n\n  private nextUpdate = this.start + 1000\n\n  constructor(\n    private readonly progressDifferentialDownloadInfo: ProgressDifferentialDownloadInfo,\n    private readonly cancellationToken: CancellationToken,\n    private readonly onProgress: (info: ProgressInfo) => any\n  ) {\n    super()\n  }\n\n  _transform(chunk: any, encoding: string, callback: any) {\n    if (this.cancellationToken.cancelled) {\n      callback(new Error(\"cancelled\"), null)\n      return\n    }\n\n    // Don't send progress update when copying from disk\n    if (this.operationType == OperationKind.COPY) {\n      callback(null, chunk)\n      return\n    }\n\n    this.transferred += chunk.length\n    this.delta += chunk.length\n\n    const now = Date.now()\n    if (\n      now >= this.nextUpdate &&\n      this.transferred !== this.expectedBytes /* will be emitted by endRangeDownload() */ &&\n      this.transferred !== this.progressDifferentialDownloadInfo.grandTotal /* will be emitted on _flush */\n    ) {\n      this.nextUpdate = now + 1000\n\n      this.onProgress({\n        total: this.progressDifferentialDownloadInfo.grandTotal,\n        delta: this.delta,\n        transferred: this.transferred,\n        percent: (this.transferred / this.progressDifferentialDownloadInfo.grandTotal) * 100,\n        bytesPerSecond: Math.round(this.transferred / ((now - this.start) / 1000)),\n      })\n      this.delta = 0\n    }\n\n    callback(null, chunk)\n  }\n\n  beginFileCopy(): void {\n    this.operationType = OperationKind.COPY\n  }\n\n  beginRangeDownload(): void {\n    this.operationType = OperationKind.DOWNLOAD\n\n    this.expectedBytes += this.progressDifferentialDownloadInfo.expectedByteCounts[this.index++]\n  }\n\n  endRangeDownload(): void {\n    // _flush() will doour final 100%\n    if (this.transferred !== this.progressDifferentialDownloadInfo.grandTotal) {\n      this.onProgress({\n        total: this.progressDifferentialDownloadInfo.grandTotal,\n        delta: this.delta,\n        transferred: this.transferred,\n        percent: (this.transferred / this.progressDifferentialDownloadInfo.grandTotal) * 100,\n        bytesPerSecond: Math.round(this.transferred / ((Date.now() - this.start) / 1000)),\n      })\n    }\n  }\n\n  // Called when we are 100% done with the connection/download\n  _flush(callback: any): void {\n    if (this.cancellationToken.cancelled) {\n      callback(new Error(\"cancelled\"))\n      return\n    }\n\n    this.onProgress({\n      total: this.progressDifferentialDownloadInfo.grandTotal,\n      delta: this.delta,\n      transferred: this.transferred,\n      percent: 100,\n      bytesPerSecond: Math.round(this.transferred / ((Date.now() - this.start) / 1000)),\n    })\n    this.delta = 0\n    this.transferred = 0\n\n    callback(null)\n  }\n}\n"]}