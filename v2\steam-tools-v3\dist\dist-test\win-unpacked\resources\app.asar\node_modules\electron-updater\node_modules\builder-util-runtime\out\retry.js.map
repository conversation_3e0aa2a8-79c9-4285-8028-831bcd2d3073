{"version": 3, "file": "retry.js", "sourceRoot": "", "sources": ["../src/retry.ts"], "names": [], "mappings": ";;AAEA,sBAYC;AAdD,2DAAuD;AAEhD,KAAK,UAAU,KAAK,CAAI,IAAsB,EAAE,UAAkB,EAAE,QAAgB,EAAE,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,WAAiC;;IACtJ,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAA;IACjD,IAAI,CAAC;QACH,OAAO,MAAM,IAAI,EAAE,CAAA;IACrB,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,IAAI,CAAC,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAG,KAAK,CAAC,mCAAI,IAAI,CAAC,IAAI,UAAU,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YACrF,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,GAAG,OAAO,GAAG,OAAO,CAAC,CAAC,CAAA;YAC/E,OAAO,MAAM,KAAK,CAAC,IAAI,EAAE,UAAU,GAAG,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,GAAG,CAAC,EAAE,WAAW,CAAC,CAAA;QACvF,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;AACH,CAAC", "sourcesContent": ["import { CancellationToken } from \"./CancellationToken\"\n\nexport async function retry<T>(task: () => Promise<T>, retryCount: number, interval: number, backoff = 0, attempt = 0, shouldRetry?: (e: any) => boolean): Promise<T> {\n  const cancellationToken = new CancellationToken()\n  try {\n    return await task()\n  } catch (error: any) {\n    if ((shouldRetry?.(error) ?? true) && retryCount > 0 && !cancellationToken.cancelled) {\n      await new Promise(resolve => setTimeout(resolve, interval + backoff * attempt))\n      return await retry(task, retryCount - 1, interval, backoff, attempt + 1, shouldRetry)\n    } else {\n      throw error\n    }\n  }\n}\n"]}