@echo off
REM Steam Tools v3 - Clean Build Script
REM This script handles file locking issues and ensures a clean build

echo ========================================
echo Steam Tools v3 - Clean Build
echo ========================================
echo.

echo Step 1: Stopping any running processes...
taskkill /F /IM "Steam Tools v3.exe" 2>nul || echo No Steam Tools processes running
taskkill /F /IM "electron.exe" 2>nul || echo No Electron processes running

echo.
echo Step 2: Building React frontend...
cd frontend
npm run build
if errorlevel 1 (
    echo ERROR: Frontend build failed
    cd ..
    pause
    exit /b 1
)
cd ..
echo Frontend build completed successfully!

echo.
echo Step 3: Building Electron app with clean output...
cd electron

REM Use a temporary directory to avoid file locks
set TEMP_DIR=dist-temp-%RANDOM%
echo Building to temporary directory: %TEMP_DIR%

REM Temporarily change output directory
powershell -Command "(Get-Content package.json) -replace '\"output\": \"../dist\"', '\"output\": \"../%TEMP_DIR%\"' | Set-Content package.json"

npm run build-dir
set BUILD_RESULT=%ERRORLEVEL%

REM Restore original output directory
powershell -Command "(Get-Content package.json) -replace '\"output\": \"../%TEMP_DIR%\"', '\"output\": \"../dist\"' | Set-Content package.json"

if %BUILD_RESULT% neq 0 (
    echo ERROR: Electron build failed
    cd ..
    pause
    exit /b 1
)

cd ..

echo.
echo Step 4: Moving build to final location...
powershell -Command "if (Test-Path 'dist') { Remove-Item -Path 'dist' -Recurse -Force -ErrorAction SilentlyContinue }; Move-Item -Path '%TEMP_DIR%' -Destination 'dist'"

echo.
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Your executable is ready at:
echo dist\win-unpacked\Steam Tools v3.exe
echo.
echo Features included:
echo - No ESLint warnings
echo - ffmpeg.dll included (no more missing DLL errors)
echo - Portable executable (no installation needed)
echo.

pause
