directories:
  output: ../dist-test
  buildResources: build
appId: com.mtyb.steamtools
productName: Steam Tools v3
downloadAlternateFFmpeg: true
files:
  - filter:
      - main.js
      - preload.js
      - ../frontend/build/**/*
      - node_modules/**/*
extraResources:
  - from: ../backend
    to: backend
    filter:
      - '**/*'
      - '!**/__pycache__/**/*'
      - '!**/*.pyc'
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
    - target: dir
      arch:
        - x64
  icon: assets/icon.ico
  requestedExecutionLevel: requireAdministrator
portable:
  artifactName: SteamTools-v3-Portable.exe
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Steam Tools v3
mac:
  target: dmg
  icon: assets/icon.icns
linux:
  target: AppImage
  icon: assets/icon.png
electronVersion: 27.3.11
