{"version": 3, "file": "xml.js", "sourceRoot": "", "sources": ["../src/xml.ts"], "names": [], "mappings": ";;;AA8EA,4BA2CC;AAzHD,2BAA0B;AAC1B,mCAAkC;AAElC,MAAa,QAAQ;IAMnB,YAAqB,IAAY;QAAZ,SAAI,GAAJ,IAAI,CAAQ;QALjC,UAAK,GAAG,EAAE,CAAA;QACV,eAAU,GAAkC,IAAI,CAAA;QAChD,YAAO,GAAG,KAAK,CAAA;QACf,aAAQ,GAA2B,IAAI,CAAA;QAGrC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAA,gBAAQ,EAAC,8BAA8B,EAAE,4BAA4B,CAAC,CAAA;QAC9E,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;YACvB,MAAM,IAAA,gBAAQ,EAAC,yBAAyB,IAAI,EAAE,EAAE,8BAA8B,CAAC,CAAA;QACjF,CAAC;IACH,CAAC;IAED,SAAS,CAAC,IAAY;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QACtE,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,MAAM,IAAA,gBAAQ,EAAC,iBAAiB,IAAI,GAAG,EAAE,0BAA0B,CAAC,CAAA;QACtE,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAED,eAAe,CAAC,IAAY;QAC1B,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QAC9B,CAAC;IACH,CAAC;IAED,OAAO,CAAC,IAAY,EAAE,UAAU,GAAG,KAAK,EAAE,gBAA+B,IAAI;QAC3E,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,CAAC,CAAA;QACnD,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YACpB,MAAM,IAAA,gBAAQ,EAAC,aAAa,IAAI,eAAe,IAAI,GAAG,EAAE,wBAAwB,CAAC,CAAA;QACnF,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAED,aAAa,CAAC,IAAY,EAAE,UAAU,GAAG,KAAK;QAC5C,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAA;QACb,CAAC;QAED,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACpC,IAAI,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,EAAE,CAAC;gBAC5C,OAAO,OAAO,CAAA;YAChB,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,WAAW,CAAC,IAAY,EAAE,UAAU,GAAG,KAAK;QAC1C,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;YAC3B,OAAO,EAAE,CAAA;QACX,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAA;IACvE,CAAC;IAED,mBAAmB,CAAC,IAAY,EAAE,UAAU,GAAG,KAAK;QAClD,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,CAAC,CAAA;QACpD,OAAO,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAA;IAC9C,CAAC;CACF;AA9DD,4BA8DC;AAED,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,6BAA6B,CAAC,CAAA;AAE9D,SAAS,WAAW,CAAC,IAAY;IAC/B,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AAChC,CAAC;AAED,SAAS,YAAY,CAAC,OAAiB,EAAE,IAAY,EAAE,UAAmB;IACxE,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAA;IAChC,OAAO,WAAW,KAAK,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,WAAW,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC,CAAA;AAChJ,CAAC;AAED,SAAgB,QAAQ,CAAC,IAAY;IACnC,IAAI,WAAW,GAAoB,IAAI,CAAA;IACvC,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;IACnC,MAAM,QAAQ,GAAoB,EAAE,CAAA;IAEpC,MAAM,CAAC,SAAS,GAAG,UAAU,CAAC,EAAE;QAC9B,MAAM,OAAO,GAAG,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QAC7C,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC,UAAoC,CAAA;QAEpE,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;YACzB,WAAW,GAAG,OAAO,CAAA;QACvB,CAAC;aAAM,CAAC;YACN,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;YAC5C,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;gBAC5B,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAA;YACtB,CAAC;YACD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAC/B,CAAC;QACD,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IACxB,CAAC,CAAA;IAED,MAAM,CAAC,UAAU,GAAG,GAAG,EAAE;QACvB,QAAQ,CAAC,GAAG,EAAE,CAAA;IAChB,CAAC,CAAA;IAED,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE;QACrB,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAA;QAC5C,CAAC;IACH,CAAC,CAAA;IAED,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE;QACvB,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAC7C,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA;QACrB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAA;IACxB,CAAC,CAAA;IAED,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,EAAE;QACrB,MAAM,GAAG,CAAA;IACX,CAAC,CAAA;IAED,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IAClB,OAAO,WAAY,CAAA;AACrB,CAAC", "sourcesContent": ["import * as sax from \"sax\"\nimport { newError } from \"./error\"\n\nexport class XElement {\n  value = \"\"\n  attributes: Record<string, string> | null = null\n  isCData = false\n  elements: Array<XElement> | null = null\n\n  constructor(readonly name: string) {\n    if (!name) {\n      throw newError(\"Element name cannot be empty\", \"ERR_XML_ELEMENT_NAME_EMPTY\")\n    }\n    if (!isValidName(name)) {\n      throw newError(`Invalid element name: ${name}`, \"ERR_XML_ELEMENT_INVALID_NAME\")\n    }\n  }\n\n  attribute(name: string): string {\n    const result = this.attributes === null ? null : this.attributes[name]\n    if (result == null) {\n      throw newError(`No attribute \"${name}\"`, \"ERR_XML_MISSED_ATTRIBUTE\")\n    }\n    return result\n  }\n\n  removeAttribute(name: string): void {\n    if (this.attributes !== null) {\n      delete this.attributes[name]\n    }\n  }\n\n  element(name: string, ignoreCase = false, errorIfMissed: string | null = null): XElement {\n    const result = this.elementOrNull(name, ignoreCase)\n    if (result === null) {\n      throw newError(errorIfMissed || `No element \"${name}\"`, \"ERR_XML_MISSED_ELEMENT\")\n    }\n    return result\n  }\n\n  elementOrNull(name: string, ignoreCase = false): XElement | null {\n    if (this.elements === null) {\n      return null\n    }\n\n    for (const element of this.elements) {\n      if (isNameEquals(element, name, ignoreCase)) {\n        return element\n      }\n    }\n\n    return null\n  }\n\n  getElements(name: string, ignoreCase = false) {\n    if (this.elements === null) {\n      return []\n    }\n    return this.elements.filter(it => isNameEquals(it, name, ignoreCase))\n  }\n\n  elementValueOrEmpty(name: string, ignoreCase = false): string {\n    const element = this.elementOrNull(name, ignoreCase)\n    return element === null ? \"\" : element.value\n  }\n}\n\nconst NAME_REG_EXP = new RegExp(/^[A-Za-z_][:A-Za-z0-9_-]*$/i)\n\nfunction isValidName(name: string) {\n  return NAME_REG_EXP.test(name)\n}\n\nfunction isNameEquals(element: XElement, name: string, ignoreCase: boolean) {\n  const elementName = element.name\n  return elementName === name || (ignoreCase === true && elementName.length === name.length && elementName.toLowerCase() === name.toLowerCase())\n}\n\nexport function parseXml(data: string): XElement {\n  let rootElement: XElement | null = null\n  const parser = sax.parser(true, {})\n  const elements: Array<XElement> = []\n\n  parser.onopentag = saxElement => {\n    const element = new XElement(saxElement.name)\n    element.attributes = saxElement.attributes as Record<string, string>\n\n    if (rootElement === null) {\n      rootElement = element\n    } else {\n      const parent = elements[elements.length - 1]\n      if (parent.elements == null) {\n        parent.elements = []\n      }\n      parent.elements.push(element)\n    }\n    elements.push(element)\n  }\n\n  parser.onclosetag = () => {\n    elements.pop()\n  }\n\n  parser.ontext = text => {\n    if (elements.length > 0) {\n      elements[elements.length - 1].value = text\n    }\n  }\n\n  parser.oncdata = cdata => {\n    const element = elements[elements.length - 1]\n    element.value = cdata\n    element.isCData = true\n  }\n\n  parser.onerror = err => {\n    throw err\n  }\n\n  parser.write(data)\n  return rootElement!\n}\n"]}