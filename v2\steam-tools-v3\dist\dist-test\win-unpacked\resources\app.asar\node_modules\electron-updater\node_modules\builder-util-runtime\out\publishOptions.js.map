{"version": 3, "file": "publishOptions.js", "sourceRoot": "", "sources": ["../src/publishOptions.ts"], "names": [], "mappings": ";;AA4IA,8BAEC;AA4PD,4DASC;AAxQD,eAAe;AACf,SAAgB,SAAS,CAAC,OAAsB,EAAE,WAAW,GAAG,YAAY;IAC1E,OAAO,GAAG,OAAO,CAAC,QAAQ,IAAI,OAAO,MAAM,OAAO,CAAC,IAAI,IAAI,WAAW,EAAE,CAAA;AAC1E,CAAC;AA4PD,SAAgB,wBAAwB,CAAC,aAAmC;IAC1E,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAA;IACvC,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;QACtB,OAAO,KAAK,CAAC,aAA0B,CAAC,CAAA;IAC1C,CAAC;IACD,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;QAC1B,OAAO,SAAS,CAAC,aAA8B,CAAC,CAAA;IAClD,CAAC;IACD,MAAM,IAAI,KAAK,CAAC,2BAA2B,QAAQ,EAAE,CAAC,CAAA;AACxD,CAAC;AAED,SAAS,KAAK,CAAC,OAAkB;IAC/B,IAAI,GAAW,CAAA;IACf,IAAI,OAAO,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC;QAC/B,GAAG,GAAG,WAAW,OAAO,CAAC,MAAM,8BAA8B,CAAA;IAC/D,CAAC;SAAM,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;QACpC,GAAG,GAAG,GAAG,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE,CAAA;IAC/C,CAAC;SAAM,IAAI,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACxC,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,gBAAgB,OAAO,CAAC,MAAM,4CAA4C,CAAC,CAAA;QAC7F,CAAC;QAED,wGAAwG;QACxG,IAAI,OAAO,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;YACnC,GAAG,GAAG,4BAA4B,OAAO,CAAC,MAAM,EAAE,CAAA;QACpD,CAAC;aAAM,CAAC;YACN,GAAG,GAAG,cAAc,OAAO,CAAC,MAAM,kBAAkB,OAAO,CAAC,MAAM,EAAE,CAAA;QACtE,CAAC;IACH,CAAC;SAAM,IAAI,OAAO,CAAC,MAAM,KAAK,YAAY,EAAE,CAAC;QAC3C,GAAG,GAAG,WAAW,OAAO,CAAC,MAAM,OAAO,OAAO,CAAC,MAAM,mBAAmB,CAAA;IACzE,CAAC;SAAM,CAAC;QACN,GAAG,GAAG,WAAW,OAAO,CAAC,MAAM,mBAAmB,CAAA;IACpD,CAAC;IACD,OAAO,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;AACtC,CAAC;AAED,SAAS,UAAU,CAAC,GAAW,EAAE,CAAmB;IAClD,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC9B,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACvB,GAAG,IAAI,GAAG,CAAA;QACZ,CAAC;QACD,GAAG,IAAI,CAAC,CAAA;IACV,CAAC;IACD,OAAO,GAAG,CAAA;AACZ,CAAC;AAED,SAAS,SAAS,CAAC,OAAsB;IACvC,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;QACzB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;IACpC,CAAC;IACD,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;QAC3B,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAA;IACtC,CAAC;IACD,OAAO,UAAU,CAAC,WAAW,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,MAAM,yBAAyB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;AACrG,CAAC", "sourcesContent": ["import { OutgoingHttpHeaders } from \"http\"\nimport { Nullish } from \".\"\n\nexport type PublishProvider = \"github\" | \"s3\" | \"spaces\" | \"generic\" | \"custom\" | \"snapStore\" | \"keygen\" | \"bitbucket\"\n\n// typescript-json-schema generates only PublishConfiguration if it is specified in the list, so, it is not added here\nexport type AllPublishOptions =\n  | string\n  | GithubOptions\n  | S3Options\n  | SpacesOptions\n  | GenericServerOptions\n  | CustomPublishOptions\n  | KeygenOptions\n  | SnapStoreOptions\n  | BitbucketOptions\n\nexport interface PublishConfiguration {\n  /**\n   * The provider.\n   */\n  readonly provider: PublishProvider\n\n  /**\n   * @private\n   * win-only\n   */\n  publisherName?: Array<string> | null\n\n  /**\n   * @private\n   * win-only\n   */\n  readonly updaterCacheDirName?: string | null\n\n  /**\n   * Whether to publish auto update info files.\n   *\n   * Auto update relies only on the first provider in the list (you can specify several publishers).\n   * Thus, probably, there`s no need to upload the metadata files for the other configured providers. But by default will be uploaded.\n   *\n   * @default true\n   */\n  readonly publishAutoUpdate?: boolean\n\n  /**\n   * Any custom request headers\n   */\n  readonly requestHeaders?: OutgoingHttpHeaders\n\n  /**\n   * Request timeout in milliseconds. (Default is 2 minutes; O is ignored)\n   *\n   * @default 120000\n   */\n  readonly timeout?: number | null\n}\n\n// https://github.com/electron-userland/electron-builder/issues/3261\nexport interface CustomPublishOptions extends PublishConfiguration {\n  /**\n   * The provider. Must be `custom`.\n   */\n  readonly provider: \"custom\"\n\n  /**\n   * The Provider to provide UpdateInfo regarding available updates.  Required\n   * to use custom providers with electron-updater.\n   */\n  updateProvider?: new (options: CustomPublishOptions, updater: any, runtimeOptions: any) => any\n\n  [index: string]: any\n}\n\n/**\n * [GitHub](https://help.github.com/articles/about-releases/) options.\n *\n * GitHub [personal access token](https://help.github.com/articles/creating-an-access-token-for-command-line-use/) is required. You can generate by going to [https://github.com/settings/tokens/new](https://github.com/settings/tokens/new). The access token should have the repo scope/permission.\n * Define `GH_TOKEN` environment variable.\n */\nexport interface GithubOptions extends PublishConfiguration {\n  /**\n   * The provider. Must be `github`.\n   */\n  readonly provider: \"github\"\n\n  /**\n   * The repository name. [Detected automatically](#github-repository-and-bintray-package).\n   */\n  readonly repo?: string | null\n\n  /**\n   * The owner.\n   */\n  readonly owner?: string | null\n\n  /**\n   * Whether to use `v`-prefixed tag name.\n   * @default true\n   */\n  readonly vPrefixedTagName?: boolean\n\n  /**\n   * The host (including the port if need).\n   * @default github.com\n   */\n  readonly host?: string | null\n\n  /**\n   * The protocol. GitHub Publisher supports only `https`.\n   * @default https\n   */\n  readonly protocol?: \"https\" | \"http\" | null\n\n  /**\n   * The access token to support auto-update from private github repositories. Never specify it in the configuration files. Only for [setFeedURL](./auto-update.md#appupdatersetfeedurloptions).\n   */\n  readonly token?: string | null\n\n  /**\n   * Whether to use private github auto-update provider if `GH_TOKEN` environment variable is defined. See [Private GitHub Update Repo](./auto-update.md#private-github-update-repo).\n   */\n  readonly private?: boolean | null\n\n  /**\n   * The channel.\n   * @default latest\n   */\n  readonly channel?: string | null\n\n  /**\n   * The type of release. By default `draft` release will be created.\n   *\n   * Also you can set release type using environment variable. If `EP_DRAFT`is set to `true` — `draft`, if `EP_PRE_RELEASE`is set to `true` — `prerelease`.\n   * @default draft\n   */\n  releaseType?: \"draft\" | \"prerelease\" | \"release\" | null\n}\n\n/** @private */\nexport function githubUrl(options: GithubOptions, defaultHost = \"github.com\") {\n  return `${options.protocol || \"https\"}://${options.host || defaultHost}`\n}\n\n/**\n * Generic (any HTTP(S) server) options.\n * In all publish options [File Macros](./file-patterns.md#file-macros) are supported.\n */\nexport interface GenericServerOptions extends PublishConfiguration {\n  /**\n   * The provider. Must be `generic`.\n   */\n  readonly provider: \"generic\"\n\n  /**\n   * The base url. e.g. `https://bucket_name.s3.amazonaws.com`.\n   */\n  readonly url: string\n\n  /**\n   * The channel.\n   * @default latest\n   */\n  readonly channel?: string | null\n\n  /**\n   * Whether to use multiple range requests for differential update. Defaults to `true` if `url` doesn't contain `s3.amazonaws.com`.\n   */\n  readonly useMultipleRangeRequest?: boolean\n}\n\n/**\n * Keygen options.\n * https://keygen.sh/\n * Define `KEYGEN_TOKEN` environment variable.\n */\nexport interface KeygenOptions extends PublishConfiguration {\n  /**\n   * The provider. Must be `keygen`.\n   */\n  readonly provider: \"keygen\"\n\n  /**\n   * Keygen host for self-hosted instances\n   * @default \"api.keygen.sh\"\n   */\n  readonly host?: string\n\n  /**\n   * Keygen account's UUID\n   */\n  readonly account: string\n\n  /**\n   * Keygen product's UUID\n   */\n  readonly product: string\n\n  /**\n   * The channel.\n   * @default stable\n   */\n  readonly channel?: \"stable\" | \"rc\" | \"beta\" | \"alpha\" | \"dev\" | null\n\n  /**\n   * The target Platform. Is set programmatically explicitly during publishing.\n   */\n  readonly platform?: string | null\n}\n\n/**\n * Bitbucket options.\n * https://bitbucket.org/\n * Define `BITBUCKET_TOKEN` environment variable.\n *\n * For converting an app password to a usable token, you can utilize this\n```typescript\nconvertAppPassword(owner: string, appPassword: string) {\n  const base64encodedData = Buffer.from(`${owner}:${appPassword.trim()}`).toString(\"base64\")\n  return `Basic ${base64encodedData}`\n}\n```\n */\nexport interface BitbucketOptions extends PublishConfiguration {\n  /**\n   * The provider. Must be `bitbucket`.\n   */\n  readonly provider: \"bitbucket\"\n\n  /**\n   * Repository owner\n   */\n  readonly owner: string\n\n  /**\n   * The [app password](https://bitbucket.org/account/settings/app-passwords) to support auto-update from private bitbucket repositories.\n   */\n  readonly token?: string | null\n\n  /**\n   * The user name to support auto-update from private bitbucket repositories.\n   */\n  readonly username?: string | null\n\n  /**\n   * Repository slug/name\n   */\n  readonly slug: string\n\n  /**\n   * The channel.\n   * @default latest\n   */\n  readonly channel?: string | null\n}\n\n/**\n * [Snap Store](https://snapcraft.io/) options. To publish directly to Snapcraft, see <a href=\"https://snapcraft.io/docs/snapcraft-authentication\">Snapcraft authentication options</a> for local or CI/CD authentication options.\n */\nexport interface SnapStoreOptions extends PublishConfiguration {\n  /**\n   * The provider. Must be `snapStore`.\n   */\n  readonly provider: \"snapStore\"\n\n  /**\n   * snapcraft repo name\n   */\n  readonly repo?: string\n\n  /**\n   * The list of channels the snap would be released.\n   * @default [\"edge\"]\n   */\n  readonly channels?: string | Array<string> | null\n}\n\nexport interface BaseS3Options extends PublishConfiguration {\n  /**\n   * The update channel.\n   * @default latest\n   */\n  channel?: string | null\n\n  /**\n   * The directory path.\n   * @default /\n   */\n  readonly path?: string | null\n\n  /**\n   * The ACL. Set to `null` to not [add](https://github.com/electron-userland/electron-builder/issues/1822).\n   *\n   * @default public-read\n   */\n  readonly acl?: \"private\" | \"public-read\" | null\n}\n\n/**\n * [Amazon S3](https://aws.amazon.com/s3/) options.\n * AWS credentials are required, please see [getting your credentials](http://docs.aws.amazon.com/sdk-for-javascript/v2/developer-guide/getting-your-credentials.html).\n * Define `AWS_ACCESS_KEY_ID` and `AWS_SECRET_ACCESS_KEY` [environment variables](http://docs.aws.amazon.com/sdk-for-javascript/v2/developer-guide/loading-node-credentials-environment.html).\n * Or in the [~/.aws/credentials](http://docs.aws.amazon.com/sdk-for-javascript/v2/developer-guide/loading-node-credentials-shared.html).\n *\n * Example configuration:\n *\n```json\n{\n  \"build\":\n    \"publish\": {\n      \"provider\": \"s3\",\n      \"bucket\": \"bucket-name\"\n    }\n  }\n}\n```\n */\nexport interface S3Options extends BaseS3Options {\n  /**\n   * The provider. Must be `s3`.\n   */\n  readonly provider: \"s3\"\n\n  /**\n   * The bucket name.\n   */\n  readonly bucket: string\n\n  /**\n   * The region. Is determined and set automatically when publishing.\n   */\n  region?: string | null\n\n  /**\n   * The ACL. Set to `null` to not [add](https://github.com/electron-userland/electron-builder/issues/1822).\n   *\n   * Please see [required permissions for the S3 provider](https://github.com/electron-userland/electron-builder/issues/1618#issuecomment-314679128).\n   *\n   * @default public-read\n   */\n  readonly acl?: \"private\" | \"public-read\" | null\n\n  /**\n   * The type of storage to use for the object.\n   * @default STANDARD\n   */\n  readonly storageClass?: \"STANDARD\" | \"REDUCED_REDUNDANCY\" | \"STANDARD_IA\" | null\n\n  /**\n   * Server-side encryption algorithm to use for the object.\n   */\n  readonly encryption?: \"AES256\" | \"aws:kms\" | null\n\n  /**\n   * The endpoint URI to send requests to. The default endpoint is built from the configured region.\n   * The endpoint should be a string like `https://{service}.{region}.amazonaws.com`.\n   */\n  readonly endpoint?: string | null\n\n  /**\n   * If set to true, this will enable the s3 accelerated endpoint\n   * These endpoints have a particular format of:\n   *  ${bucketname}.s3-accelerate.amazonaws.com\n   */\n  readonly accelerate?: boolean\n\n  /**\n   * When true, force a path-style endpoint to be used where the bucket name is part of the path.\n   * [Path-style Access](https://docs.aws.amazon.com/AmazonS3/latest/userguide/VirtualHosting.html#path-style-access)\n   */\n  readonly forcePathStyle?: boolean\n}\n\n/**\n * [DigitalOcean Spaces](https://www.digitalocean.com/community/tutorials/an-introduction-to-digitalocean-spaces) options.\n * Access key is required, define `DO_KEY_ID` and `DO_SECRET_KEY` environment variables.\n */\nexport interface SpacesOptions extends BaseS3Options {\n  /**\n   * The provider. Must be `spaces`.\n   */\n  readonly provider: \"spaces\"\n\n  /**\n   * The space name.\n   */\n  readonly name: string\n\n  /**\n   * The region (e.g. `nyc3`).\n   */\n  readonly region: string\n}\n\nexport function getS3LikeProviderBaseUrl(configuration: PublishConfiguration) {\n  const provider = configuration.provider\n  if (provider === \"s3\") {\n    return s3Url(configuration as S3Options)\n  }\n  if (provider === \"spaces\") {\n    return spacesUrl(configuration as SpacesOptions)\n  }\n  throw new Error(`Not supported provider: ${provider}`)\n}\n\nfunction s3Url(options: S3Options) {\n  let url: string\n  if (options.accelerate == true) {\n    url = `https://${options.bucket}.s3-accelerate.amazonaws.com`\n  } else if (options.endpoint != null) {\n    url = `${options.endpoint}/${options.bucket}`\n  } else if (options.bucket.includes(\".\")) {\n    if (options.region == null) {\n      throw new Error(`Bucket name \"${options.bucket}\" includes a dot, but S3 region is missing`)\n    }\n\n    // special case, see http://docs.aws.amazon.com/AmazonS3/latest/dev/UsingBucket.html#access-bucket-intro\n    if (options.region === \"us-east-1\") {\n      url = `https://s3.amazonaws.com/${options.bucket}`\n    } else {\n      url = `https://s3-${options.region}.amazonaws.com/${options.bucket}`\n    }\n  } else if (options.region === \"cn-north-1\") {\n    url = `https://${options.bucket}.s3.${options.region}.amazonaws.com.cn`\n  } else {\n    url = `https://${options.bucket}.s3.amazonaws.com`\n  }\n  return appendPath(url, options.path)\n}\n\nfunction appendPath(url: string, p: string | Nullish): string {\n  if (p != null && p.length > 0) {\n    if (!p.startsWith(\"/\")) {\n      url += \"/\"\n    }\n    url += p\n  }\n  return url\n}\n\nfunction spacesUrl(options: SpacesOptions) {\n  if (options.name == null) {\n    throw new Error(`name is missing`)\n  }\n  if (options.region == null) {\n    throw new Error(`region is missing`)\n  }\n  return appendPath(`https://${options.name}.${options.region}.digitaloceanspaces.com`, options.path)\n}\n"]}